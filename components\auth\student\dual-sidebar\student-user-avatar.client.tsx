'use client';
import { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from '../../../ui/avatar.client';
import { AvatarUpload } from '../../../ui/avatar-upload.client';
import { Popover } from '@base-ui-components/react/popover';
import { useStudentAuth } from '../../../../lib/auth-client';
import { useRedirect } from 'blade/hooks';
import { Link } from 'blade/client/components';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import {
  LogOut,
  X,
  Settings,
  ArrowLeft,
  User
} from 'lucide-react';

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// Terms/Privacy with Logout Button Overlay (simplified version for student)
function TermsPrivacyWithLogout() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const transition = { type: 'spring' as const, stiffness: 400, damping: 30 };

  // Auth hooks
  const { signOut } = useStudentAuth();
  const redirect = useRedirect();

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!buttonRef.current) return;
    const rect = buttonRef.current.getBoundingClientRect();
    setPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);
  const handleCancel = () => setIsExpanded(false);
  const handleInitialClick = () => setIsExpanded(true);

  const handleLogout = async () => {
    console.log('Student logout button clicked - starting sign out process');

    // Set signing out state to trigger prefetching with role-specific URL
    setIsSigningOut(true);

    // Sign out with prefetching - shorter delay since login page is prefetched
    await signOut();

    // Small delay to let prefetching work, then redirect to student login
    setTimeout(() => {
      redirect('/login?role=student');
    }, 100);

    console.log('Student sign out complete with prefetched redirect to student login.');
  };

  // Prefetch student login page when signing out
  const PrefetchLoginLink = isSigningOut ? (
    <Link href="/login?role=student" prefetch={true} className="hidden">
      <a>Prefetch student login</a>
    </Link>
  ) : null;

  return (
    <div className="relative h-8 w-full group select-none">
      {PrefetchLoginLink}
      {/* Base Layer - Terms and Privacy (always visible) */}
      <div className="flex items-center justify-between w-full">
        {/* Terms and Privacy */}
        <div className="flex gap-6 text-xs text-black/60 dark:text-white/60 items-center">
          <span className="hover:text-black/80 dark:hover:text-white/80 transition-colors duration-100 cursor-pointer">Terms</span>
          <span className="h-4 w-px bg-black/30 dark:bg-white/30" />
          <span className="hover:text-black/80 dark:hover:text-white/80 transition-colors duration-100 cursor-pointer">Privacy</span>
        </div>

        {/* Logout X Button */}
        <div
          ref={buttonRef}
          className="flex-shrink-0 relative"
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <button
            onClick={handleInitialClick}
            className={cn(
              'group cursor-pointer relative flex items-center justify-center overflow-hidden rounded-lg border border-red-500/20 bg-gradient-to-r from-red-900/60 dark:from-red-900/20 to-red-950/60 dark:to-red-950/20 px-2 py-1 text-xs font-light text-red-500 dark:text-red-300 shadow-sm transition-all duration-300 hover:border-red-500/30 hover:text-red-800 dark:hover:text-red-200 focus:outline-none'
            )}
          >
            <X className="h-3 w-3" />
          </button>

          {/* Animated border effect - only for X button */}
          <div
            className="pointer-events-none absolute inset-0 rounded-lg border-2 border-red-500/50 dark:border-red-400/50 transition-opacity duration-500"
            style={{
              opacity,
              WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
              maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
            } as React.CSSProperties}
          />
        </div>
      </div>

      {/* Overlay Layer - Logout Confirmation */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={transition}
            className="absolute inset-0 z-50 flex items-center justify-center backdrop-blur-md bg-black/10 dark:bg-white/5 rounded-lg"
          >
            {/* Logout Confirmation Content */}
            <div className="flex items-center w-full justify-between overflow-hidden rounded-lg border border-red-500/20 bg-gradient-to-r from-red-900/20 to-red-950/20 px-3 py-1 shadow-lg backdrop-blur-sm whitespace-nowrap">
              {/* Sign out text */}
              <div className="text-xs text-red-300 font-medium whitespace-nowrap">
                Sign out?
              </div>

              {/* Yes button */}
              <div className="flex gap-2">
                <button
                  onClick={handleLogout}
                  className={cn(
                    'cursor-pointer relative flex items-center justify-center overflow-hidden rounded border border-red-500/30 bg-gradient-to-r from-red-800/30 to-red-900/30 px-2 py-0.5 text-xs font-light text-red-200 transition-all duration-200 hover:border-red-500/50 hover:text-red-100 focus:outline-none'
                  )}
                >
                  <div className="flex items-center">
                    <LogOut className="mr-1 h-2.5 w-2.5" />
                    <span>Yes</span>
                  </div>
                </button>

                {/* No button */}
                <button
                  onClick={handleCancel}
                  className={cn(
                    'cursor-pointer relative flex items-center justify-center overflow-hidden rounded border border-gray-500/30 bg-gradient-to-r from-gray-800/30 to-gray-900/30 px-2 py-0.5 text-xs font-light text-gray-200 transition-all duration-200 hover:border-gray-500/50 hover:text-gray-100 focus:outline-none'
                  )}
                >
                  No
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

type PopoverView = 'main' | 'profile';

export function StudentUserAvatar() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentView, setCurrentView] = useState<PopoverView>('main');
  const { session } = useStudentAuth();

  // If no session, don't render the avatar
  if (!session?.user) {
    return null;
  }

  const user = session.user;
  const userInitials = getUserInitials(user.name || user.email || 'Student');

  // Handle view changes
  const handleShowProfile = () => {
    setCurrentView('profile');
  };

  const handleBackToMain = () => {
    setCurrentView('main');
  };

  // Handle avatar update
  const handleAvatarUpdate = (imageUrl: string | null) => {
    console.log('Student avatar updated:', imageUrl);
    // The avatar update hook will handle the actual database update
    // and refresh the session, so the UI will update automatically
  };

  // Reset view when popover closes
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setCurrentView('main');
    }
  };

  return (
    <div className="fixed top-2 right-2 z-[100000000]">
      <Popover.Root onOpenChange={handleOpenChange}>
        <Popover.Trigger className={cn(
          "flex items-center gap-2 px-2 py-1.5 border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 select-none hover:bg-gray-100 dark:hover:bg-gray-700 focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-blue-800 active:bg-gray-100 dark:active:bg-gray-700 transition-all duration-200",
          isOpen ? "rounded-xl" : "rounded-full"
        )}>
          <Avatar className="h-8 w-8 ring-2 ring-black/20 dark:ring-white/20">
            <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
            <AvatarFallback className="bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-500 dark:from-blue-500 dark:via-indigo-600 dark:to-purple-600 text-white font-semibold text-sm">
              {userInitials}
            </AvatarFallback>
          </Avatar>
        </Popover.Trigger>
        
        <Popover.Portal>
          <Popover.Positioner sideOffset={8} align="end">
            <Popover.Popup className="origin-[var(--transform-origin)] rounded-xl bg-white dark:bg-gray-800 px-0 py-0 text-gray-900 dark:text-gray-100 shadow-lg shadow-gray-200 dark:shadow-gray-900 outline-1 outline-gray-200 dark:outline-gray-700 transition-[transform,scale,opacity] data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 min-w-[280px]">

              {currentView === 'main' ? (
                <>
                  {/* Header with student info */}
                  <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-500 dark:from-blue-500 dark:via-indigo-600 dark:to-purple-600 text-white font-semibold">
                          {userInitials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {user.name || 'Student'}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {user.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Profile Settings Button */}
                  <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handleShowProfile}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                    >
                      <Settings className="h-4 w-4" />
                      Profile Settings
                    </button>
                  </div>

                  {/* Footer with logout */}
                  <div className="px-4 py-3">
                    <TermsPrivacyWithLogout />
                  </div>
                </>
              ) : (
                <>
                  {/* Profile Management Header */}
                  <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={handleBackToMain}
                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </button>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <h3 className="text-sm font-medium">Profile Settings</h3>
                      </div>
                    </div>
                  </div>

                  {/* Avatar Upload Section */}
                  <div className="px-4 py-4">
                    <div className="flex flex-col items-center gap-4">
                      <AvatarUpload
                        currentImage={user.image}
                        userInitials={userInitials}
                        userName={user.name || user.email}
                        onImageUpdate={handleAvatarUpdate}
                        size="md"
                      />
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {user.name || 'Student'}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {user.email}
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </Popover.Popup>
          </Popover.Positioner>
        </Popover.Portal>
      </Popover.Root>
    </div>
  );
}
