// Blade's StoredObject type for blob fields
export interface StoredObject {
  key: string;
  src: string;
  meta: {
    size: number;
    width?: number;
    height?: number;
    type: string;
  };
  placeholder?: {
    base64: string;
  };
}

/**
 * Helper function to get image URL from various formats
 * Handles both string URLs and Blade's StoredObject blob format
 */
export const getImageUrl = (image: string | StoredObject | null | undefined): string | undefined => {
  console.log('🔍 getImageUrl called with:', { image, type: typeof image });

  if (!image) return undefined;

  // Handle string URLs (legacy or direct URLs)
  if (typeof image === 'string') {
    console.log('📝 Processing string image:', image);
    // If it's already a full URL, return as-is
    if (image.startsWith('http://') || image.startsWith('https://') || image.startsWith('data:')) {
      console.log('✅ Already full URL, returning as-is');
      return image;
    }
    // If it's just a filename, construct the RONIN storage URL
    // RONIN typically serves blobs from a storage endpoint
    const constructedUrl = `https://storage.ronin.co/${image}`;
    console.log('🔗 Constructed URL from filename:', constructedUrl);
    return constructedUrl;
  }

  // Handle StoredObject format from Blade
  if (typeof image === 'object' && image !== null && 'src' in image) {
    console.log('📦 Processing StoredObject:', image);
    const src = image.src;
    // If src is already a full URL, return as-is
    if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {
      console.log('✅ StoredObject src is full URL, returning as-is');
      return src;
    }
    // If it's just a filename, construct the RONIN storage URL
    const constructedUrl = `https://storage.ronin.co/${src}`;
    console.log('🔗 Constructed URL from StoredObject src:', constructedUrl);
    return constructedUrl;
  }

  console.log('❌ Unknown image format, returning undefined');
  return undefined;
};

/**
 * Helper function to get placeholder image for loading states
 * Returns the base64 placeholder if available from StoredObject
 */
export const getImagePlaceholder = (image: string | StoredObject | null | undefined): string | undefined => {
  if (!image) return undefined;
  if (typeof image === 'object' && 'placeholder' in image && image.placeholder) {
    return image.placeholder.base64;
  }
  return undefined;
};

/**
 * Helper function to check if an image is a StoredObject (blob)
 */
export const isStoredObject = (image: any): image is StoredObject => {
  return image && typeof image === 'object' && 'src' in image && 'key' in image && 'meta' in image;
};
