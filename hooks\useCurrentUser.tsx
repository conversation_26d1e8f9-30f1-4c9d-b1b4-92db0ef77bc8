'use client';
import { useUnifiedSession } from '../lib/auth-client';
import { useMutation } from 'blade/client/hooks';
import { useState, useEffect } from 'react';

/**
 * Custom hook that provides reactive user data from Blade's data system
 * This ensures that when user data is updated via mutations, the UI updates automatically
 */
export function useCurrentUser() {
  const { session } = useUnifiedSession();
  const { get } = useMutation();
  const [user, setUser] = useState(session?.user || null);
  const [isLoading, setIsLoading] = useState(false);

  // Function to fetch fresh user data from Blade
  const refreshUser = async () => {
    if (!session?.user?.id) {
      setUser(null);
      return;
    }

    try {
      setIsLoading(true);
      // Use Blade's query system to get fresh user data
      const freshUser = await get.user({
        with: { id: session.user.id }
      });
      
      console.log('🔄 Refreshed user data from Blade:', freshUser);
      setUser(freshUser);
    } catch (error) {
      console.error('❌ Failed to refresh user data:', error);
      // Fallback to session user if Blade query fails
      setUser(session?.user || null);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh user data when session changes
  useEffect(() => {
    refreshUser();
  }, [session?.user?.id]);

  // Auto-refresh user data periodically to catch updates from mutations
  useEffect(() => {
    if (!session?.user?.id) return;

    const interval = setInterval(() => {
      refreshUser();
    }, 2000); // Refresh every 2 seconds to catch mutations

    return () => clearInterval(interval);
  }, [session?.user?.id]);

  return {
    user,
    isLoading,
    refreshUser
  };
}
