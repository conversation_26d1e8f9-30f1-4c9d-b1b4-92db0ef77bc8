import { use } from 'blade/server/hooks';
import { auth } from '../../../../lib/auth';
import { UserNav } from './user-nav.client';

export default async function UserNavServer(props: any) {
  // Get the session from Better Auth on the server side
  const session = await auth.api.getSession({
    headers: props.headers || {}
  });

  if (!session?.user?.id) {
    return <UserNav {...props} user={null} />;
  }

  // Use Blade's reactive data system to get the current user
  // This will automatically update when the user data changes
  const user = use.user({
    with: { id: session.user.id }
  });

  return <UserNav {...props} user={user} />;
}
