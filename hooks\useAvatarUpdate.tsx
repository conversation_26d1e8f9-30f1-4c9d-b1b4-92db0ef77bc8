'use client';
import { useState, useCallback } from 'react';

import { useUnifiedSession } from '../lib/auth-client';

interface UseAvatarUpdateReturn {
  updateAvatar: (file: File | null) => Promise<void>;
  isUpdating: boolean;
  error: string | null;
}

export function useAvatarUpdate(): UseAvatarUpdateReturn {
  const { session, refreshSession } = useUnifiedSession();
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateAvatar = useCallback(async (file: File | null) => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      if (file) {
        // Validate file
        if (!file.type.startsWith('image/')) {
          throw new Error('Please select an image file');
        }

        if (file.size > 5 * 1024 * 1024) {
          throw new Error('Image must be smaller than 5MB');
        }

        // Validate image type
        const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!supportedTypes.includes(file.type)) {
          throw new Error('Supported formats: JPEG, PNG, WebP');
        }

        console.log('Uploading avatar:', {
          name: file.name,
          size: file.size,
          type: file.type
        });

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('avatar', file);

        // Upload via API endpoint
        const response = await fetch('/api/upload-avatar', {
          method: 'POST',
          body: formData,
          credentials: 'include' // Include session cookies
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        console.log('Avatar uploaded successfully');
      } else {
        // Remove avatar via API endpoint
        const response = await fetch('/api/upload-avatar', {
          method: 'DELETE',
          credentials: 'include' // Include session cookies
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Avatar removal failed');
        }

        console.log('Avatar removed successfully');
      }

      // Refresh the session to get updated user data with the new blob URL
      console.log('🔄 Refreshing session after avatar update...');

      await refreshSession();
      console.log('✅ Session refreshed successfully');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Avatar update failed';
      setError(errorMessage);
      console.error('Avatar update failed:', err);
    } finally {
      setIsUpdating(false);
    }
  }, [session?.user?.id, refreshSession]);

  return {
    updateAvatar,
    isUpdating,
    error
  };
}

// Hook specifically for getting user initials
export function useUserInitials(name?: string): string {
  if (!name) return 'U';
  
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
