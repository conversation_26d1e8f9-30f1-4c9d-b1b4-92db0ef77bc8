'use client';
import { useState, useCallback } from 'react';
import { useMutation } from 'blade/client/hooks';
import { useUnifiedSession } from '../lib/auth-client';

interface AvatarUpdateResult {
  key: string;
  src: string;
  meta: {
    size: number;
    width: number;
    height: number;
    type: string;
  };
  placeholder: {
    base64: string;
  };
}

interface UseAvatarUpdateReturn {
  updateAvatar: (file: File | null) => Promise<void>;
  isUpdating: boolean;
  error: string | null;
}

export function useAvatarUpdate(): UseAvatarUpdateReturn {
  const { session, refreshSession } = useUnifiedSession();
  const { set } = useMutation();
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateAvatar = useCallback(async (file: File | null) => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      if (file) {
        // Validate file
        if (!file.type.startsWith('image/')) {
          throw new Error('Please select an image file');
        }

        if (file.size > 5 * 1024 * 1024) {
          throw new Error('Image must be smaller than 5MB');
        }

        // For now, we'll create a mock blob structure
        // In a real implementation, this would upload to RONIN's blob storage
        const mockBlobResult: AvatarUpdateResult = {
          key: `avatar-${session.user.id}-${Date.now()}`,
          src: URL.createObjectURL(file),
          meta: {
            size: file.size,
            width: 400, // Would be determined by actual image processing
            height: 400,
            type: file.type
          },
          placeholder: {
            base64: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
          }
        };

        // Simulate upload delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update user record with new image using Blade's mutation
        await set.user({
          with: { id: session.user.id },
          to: { image: mockBlobResult }
        });

        console.log('Avatar updated successfully:', mockBlobResult);
      } else {
        // Remove avatar - set image to null
        await new Promise(resolve => setTimeout(resolve, 500));

        await set.user({
          with: { id: session.user.id },
          to: { image: null }
        });

        console.log('Avatar removed successfully');
      }

      // Refresh the session to get updated user data
      await refreshSession();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Avatar update failed';
      setError(errorMessage);
      console.error('Avatar update failed:', err);
    } finally {
      setIsUpdating(false);
    }
  }, [session?.user?.id, set, refreshSession]);

  return {
    updateAvatar,
    isUpdating,
    error
  };
}

// Hook specifically for getting user initials
export function useUserInitials(name?: string): string {
  if (!name) return 'U';
  
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
